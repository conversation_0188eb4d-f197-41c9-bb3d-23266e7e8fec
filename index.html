<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lab Equipment Scheduler Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .timeline {
            flex: 1;
            padding: 20px;
            border-right: 2px solid #e0e0e0;
            position: relative;
        }

        .workspace-track {
            margin-bottom: 40px;
            position: relative;
        }

        .workspace-label {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f0f0f0;
            border-radius: 4px;
            display: inline-block;
        }

        .time-track {
            height: 40px;
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
            display: flex;
            align-items: center;
        }

        .time-markers {
            position: absolute;
            bottom: -25px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }

        .now-indicator {
            position: absolute;
            top: -10px;
            bottom: -30px;
            width: 3px;
            background: #007bff;
            border-radius: 2px;
            z-index: 10;
        }

        .info-panel {
            flex: 1;
            padding: 20px;
            background: #fafafa;
        }

        .status-grid {
            margin-bottom: 30px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .status-label {
            font-weight: 500;
        }

        .status-value {
            color: #666;
        }

        .status-value.active { color: #28a745; }
        .status-value.locked { color: #dc3545; }
        .status-value.available { color: #28a745; }

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .btn {
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            text-decoration: none;
            text-align: center;
            background: white;
            color: #333;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .btn:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }

        .btn.disabled {
            opacity: 0.5;
            pointer-events: none;
            cursor: not-allowed;
        }

        .debug-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-family: monospace;
        }

        /* Modal styles */
        .modal {
            position: fixed;
            inset: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal:target {
            display: flex;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            text-decoration: none;
            color: #666;
        }

        .modal-title {
            margin-bottom: 15px;
            color: #dc3545;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn-overtake {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .btn-cancel {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        /* Hide all sections by default */
        section {
            display: none;
        }

        /* Show targeted section */
        section:target {
            display: block;
        }

        /* Show first section by default */
        section:first-of-type {
            display: block;
        }

        section:first-of-type:not(:target) ~ section:target {
            display: block;
        }

        section:first-of-type:not(:target) ~ section:target ~ section:first-of-type {
            display: none;
        }

        /* Time indicator positioning */
        .h09 .now-indicator { left: 0%; }
        .h10 .now-indicator { left: 11.11%; }
        .h11 .now-indicator { left: 22.22%; }
        .h12 .now-indicator { left: 33.33%; }
        .h13 .now-indicator { left: 44.44%; }
        .h14 .now-indicator { left: 55.55%; }
        .h15 .now-indicator { left: 66.66%; }
        .h16 .now-indicator { left: 77.77%; }
        .h17 .now-indicator { left: 88.88%; }
        .h18 .now-indicator { left: 100%; }
    </style>
</head>
<body>
    <!-- Initial state: 09:00, Device Available, Both Standby -->
    <section id="H09-DN-Astd-Tstd" class="h09">
        <div class="container">
            <div class="timeline">
                <div class="workspace-track">
                    <div class="workspace-label">Anke's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="workspace-track">
                    <div class="workspace-label">Tom's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="time-markers">
                    <span>9AM</span><span>10AM</span><span>11AM</span><span>12PM</span><span>1PM</span><span>2PM</span><span>3PM</span><span>4PM</span><span>5PM</span><span>6PM</span>
                </div>
            </div>
            <div class="info-panel">
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Current Active Workspace:</span>
                        <span class="status-value">None</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Device Lock Status:</span>
                        <span class="status-value available">Available</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Data Annotation:</span>
                        <span class="status-value">No annotations</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">System Message:</span>
                        <span class="status-value">Ready to start</span>
                    </div>
                </div>
                <div class="actions">
                    <a href="#H10-DA-Aact-Tstd" class="btn">Start Anke</a>
                    <a href="#H10-DT-Astd-Tact" class="btn">Start Tom</a>
                    <a href="#H10-DN-Astd-Tstd" class="btn">Set Anke to Standby</a>
                    <a href="#H10-DN-Astd-Tstd" class="btn">Set Anke to Closed</a>
                    <a href="#H10-DN-Astd-Tstd" class="btn">Set Tom to Standby</a>
                    <a href="#H10-DN-Astd-Tclo" class="btn">Set Tom to Closed</a>
                    <a href="#H10-DN-Astd-Tstd" class="btn">Take device from Anke</a>
                    <a href="#H10-DN-Astd-Tstd" class="btn">Take device from Tom</a>
                </div>
                <div class="debug-badge">H09-DN-Astd-Tstd</div>
            </div>
        </div>
    </section>

    <!-- 10:00 - Anke Active with Device -->
    <section id="H10-DA-Aact-Tstd" class="h10">
        <div class="container">
            <div class="timeline">
                <div class="workspace-track">
                    <div class="workspace-label">Anke's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="workspace-track">
                    <div class="workspace-label">Tom's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="time-markers">
                    <span>9AM</span><span>10AM</span><span>11AM</span><span>12PM</span><span>1PM</span><span>2PM</span><span>3PM</span><span>4PM</span><span>5PM</span><span>6PM</span>
                </div>
            </div>
            <div class="info-panel">
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Current Active Workspace:</span>
                        <span class="status-value active">Anke</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Device Lock Status:</span>
                        <span class="status-value locked">Locked by Anke</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Data Annotation:</span>
                        <span class="status-value">Annotating Anke</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">System Message:</span>
                        <span class="status-value">Running Anke</span>
                    </div>
                </div>
                <div class="actions">
                    <a href="#H11-DA-Aact-Tstd" class="btn">Start Anke</a>
                    <a href="#conflict-tom-from-anke-H11" class="btn">Start Tom</a>
                    <a href="#H11-DN-Astd-Tstd" class="btn">Set Anke to Standby</a>
                    <a href="#H11-DN-Aclo-Tstd" class="btn">Set Anke to Closed</a>
                    <a href="#H11-DA-Aact-Tstd" class="btn">Set Tom to Standby</a>
                    <a href="#H11-DA-Aact-Tclo" class="btn">Set Tom to Closed</a>
                    <a href="#H11-DA-Aact-Tstd" class="btn">Take device from Anke</a>
                    <a href="#conflict-tom-from-anke-H11" class="btn">Take device from Tom</a>
                </div>
                <div class="debug-badge">H10-DA-Aact-Tstd</div>
            </div>
        </div>
    </section>

    <!-- 10:00 - Tom Active with Device -->
    <section id="H10-DT-Astd-Tact" class="h10">
        <div class="container">
            <div class="timeline">
                <div class="workspace-track">
                    <div class="workspace-label">Anke's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="workspace-track">
                    <div class="workspace-label">Tom's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="time-markers">
                    <span>9AM</span><span>10AM</span><span>11AM</span><span>12PM</span><span>1PM</span><span>2PM</span><span>3PM</span><span>4PM</span><span>5PM</span><span>6PM</span>
                </div>
            </div>
            <div class="info-panel">
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Current Active Workspace:</span>
                        <span class="status-value active">Tom</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Device Lock Status:</span>
                        <span class="status-value locked">Locked by Tom</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Data Annotation:</span>
                        <span class="status-value">Annotating Tom</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">System Message:</span>
                        <span class="status-value">Running Tom</span>
                    </div>
                </div>
                <div class="actions">
                    <a href="#conflict-anke-from-tom-H11" class="btn">Start Anke</a>
                    <a href="#H11-DT-Astd-Tact" class="btn">Start Tom</a>
                    <a href="#H11-DT-Astd-Tact" class="btn">Set Anke to Standby</a>
                    <a href="#H11-DT-Aclo-Tact" class="btn">Set Anke to Closed</a>
                    <a href="#H11-DN-Astd-Tstd" class="btn">Set Tom to Standby</a>
                    <a href="#H11-DN-Astd-Tclo" class="btn">Set Tom to Closed</a>
                    <a href="#conflict-anke-from-tom-H11" class="btn">Take device from Anke</a>
                    <a href="#H11-DT-Astd-Tact" class="btn">Take device from Tom</a>
                </div>
                <div class="debug-badge">H10-DT-Astd-Tact</div>
            </div>
        </div>
    </section>

    <!-- 10:00 - Tom Closed -->
    <section id="H10-DN-Astd-Tclo" class="h10">
        <div class="container">
            <div class="timeline">
                <div class="workspace-track">
                    <div class="workspace-label">Anke's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="workspace-track">
                    <div class="workspace-label">Tom's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="time-markers">
                    <span>9AM</span><span>10AM</span><span>11AM</span><span>12PM</span><span>1PM</span><span>2PM</span><span>3PM</span><span>4PM</span><span>5PM</span><span>6PM</span>
                </div>
            </div>
            <div class="info-panel">
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Current Active Workspace:</span>
                        <span class="status-value">None</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Device Lock Status:</span>
                        <span class="status-value available">Available</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Data Annotation:</span>
                        <span class="status-value">No annotations</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">System Message:</span>
                        <span class="status-value">Workspace set to Closed</span>
                    </div>
                </div>
                <div class="actions">
                    <a href="#H11-DA-Aact-Tclo" class="btn">Start Anke</a>
                    <a href="#H11-DN-Astd-Tclo" class="btn">Start Tom</a>
                    <a href="#H11-DN-Astd-Tclo" class="btn">Set Anke to Standby</a>
                    <a href="#H11-DN-Aclo-Tclo" class="btn">Set Anke to Closed</a>
                    <a href="#H11-DN-Astd-Tstd" class="btn">Set Tom to Standby</a>
                    <a href="#H11-DN-Astd-Tclo" class="btn">Set Tom to Closed</a>
                    <a href="#H11-DN-Astd-Tclo" class="btn">Take device from Anke</a>
                    <a href="#H11-DN-Astd-Tclo" class="btn">Take device from Tom</a>
                </div>
                <div class="debug-badge">H10-DN-Astd-Tclo</div>
            </div>
        </div>
    </section>

    <!-- Conflict Modals -->
    <div id="conflict-tom-from-anke-H11" class="modal" role="dialog">
        <div class="modal-content">
            <a href="#H10-DA-Aact-Tstd" class="modal-close">&times;</a>
            <h3 class="modal-title">⚠️ Conflict: Device locked by Anke</h3>
            <p>The device is currently being used by Anke's workspace. What would you like to do?</p>
            <div class="modal-actions">
                <a href="#H11-DT-Astd-Tact" class="btn btn-overtake">Overtake</a>
                <a href="#H11-DA-Aact-Tstd" class="btn btn-cancel">Cancel</a>
            </div>
        </div>
    </div>

    <div id="conflict-anke-from-tom-H11" class="modal" role="dialog">
        <div class="modal-content">
            <a href="#H10-DT-Astd-Tact" class="modal-close">&times;</a>
            <h3 class="modal-title">⚠️ Conflict: Device locked by Tom</h3>
            <p>The device is currently being used by Tom's workspace. What would you like to do?</p>
            <div class="modal-actions">
                <a href="#H11-DA-Aact-Tstd" class="btn btn-overtake">Overtake</a>
                <a href="#H11-DT-Astd-Tact" class="btn btn-cancel">Cancel</a>
            </div>
        </div>
    </div>

    <!-- 11:00 States -->
    <!-- 11:00 - Both Standby, Device Available -->
    <section id="H11-DN-Astd-Tstd" class="h11">
        <div class="container">
            <div class="timeline">
                <div class="workspace-track">
                    <div class="workspace-label">Anke's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="workspace-track">
                    <div class="workspace-label">Tom's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="time-markers">
                    <span>9AM</span><span>10AM</span><span>11AM</span><span>12PM</span><span>1PM</span><span>2PM</span><span>3PM</span><span>4PM</span><span>5PM</span><span>6PM</span>
                </div>
            </div>
            <div class="info-panel">
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Current Active Workspace:</span>
                        <span class="status-value">None</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Device Lock Status:</span>
                        <span class="status-value available">Available</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Data Annotation:</span>
                        <span class="status-value">No annotations</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">System Message:</span>
                        <span class="status-value">Workspace set to Standby</span>
                    </div>
                </div>
                <div class="actions">
                    <a href="#H12-DA-Aact-Tstd" class="btn">Start Anke</a>
                    <a href="#H12-DT-Astd-Tact" class="btn">Start Tom</a>
                    <a href="#H12-DN-Astd-Tstd" class="btn">Set Anke to Standby</a>
                    <a href="#H12-DN-Aclo-Tstd" class="btn">Set Anke to Closed</a>
                    <a href="#H12-DN-Astd-Tstd" class="btn">Set Tom to Standby</a>
                    <a href="#H12-DN-Astd-Tclo" class="btn">Set Tom to Closed</a>
                    <a href="#H12-DN-Astd-Tstd" class="btn">Take device from Anke</a>
                    <a href="#H12-DN-Astd-Tstd" class="btn">Take device from Tom</a>
                </div>
                <div class="debug-badge">H11-DN-Astd-Tstd</div>
            </div>
        </div>
    </section>

    <!-- 11:00 - Anke Active with Device -->
    <section id="H11-DA-Aact-Tstd" class="h11">
        <div class="container">
            <div class="timeline">
                <div class="workspace-track">
                    <div class="workspace-label">Anke's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="workspace-track">
                    <div class="workspace-label">Tom's workspace</div>
                    <div class="time-track">
                        <div class="now-indicator"></div>
                    </div>
                </div>
                <div class="time-markers">
                    <span>9AM</span><span>10AM</span><span>11AM</span><span>12PM</span><span>1PM</span><span>2PM</span><span>3PM</span><span>4PM</span><span>5PM</span><span>6PM</span>
                </div>
            </div>
            <div class="info-panel">
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Current Active Workspace:</span>
                        <span class="status-value active">Anke</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Device Lock Status:</span>
                        <span class="status-value locked">Locked by Anke</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Data Annotation:</span>
                        <span class="status-value">Annotating Anke</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">System Message:</span>
                        <span class="status-value">Device moved to Anke</span>
                    </div>
                </div>
                <div class="actions">
                    <a href="#H12-DA-Aact-Tstd" class="btn">Start Anke</a>
                    <a href="#conflict-tom-from-anke-H12" class="btn">Start Tom</a>
                    <a href="#H12-DN-Astd-Tstd" class="btn">Set Anke to Standby</a>
                    <a href="#H12-DN-Aclo-Tstd" class="btn">Set Anke to Closed</a>
                    <a href="#H12-DA-Aact-Tstd" class="btn">Set Tom to Standby</a>
                    <a href="#H12-DA-Aact-Tclo" class="btn">Set Tom to Closed</a>
                    <a href="#H12-DA-Aact-Tstd" class="btn">Take device from Anke</a>
                    <a href="#conflict-tom-from-anke-H12" class="btn">Take device from Tom</a>
                </div>
                <div class="debug-badge">H11-DA-Aact-Tstd</div>
            </div>
        </div>
    </section>
