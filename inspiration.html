<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workspace Data Annotation System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        
        .diagram-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        h3 {
            color: #555;
            margin-top: 20px;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        /* Timeline Diagram Styles */
        .timeline-container {
            position: relative;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            overflow-x: auto;
        }
        
        .timeline {
            position: relative;
            height: 200px;
            margin: 20px 0;
        }
        
        .timeline-line {
            position: absolute;
            top: 100px;
            left: 0;
            right: 0;
            height: 2px;
            background: #ddd;
        }
        
        .timeline-segment {
            position: absolute;
            top: 85px;
            height: 30px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .timeline-segment:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .workspace-a {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        
        .workspace-b {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }
        
        .no-workspace {
            background: #95a5a6;
        }
        
        .timeline-label {
            position: absolute;
            top: 130px;
            font-size: 12px;
            color: #666;
        }
        
        .data-point {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            top: 96px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.5);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        /* Flow Diagram Styles */
        .flow-diagram {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .flow-box {
            background: white;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 20px;
            width: 200px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .flow-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.2);
        }
        
        .flow-box.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .flow-arrow {
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: #667eea;
        }
        
        /* Locking Mechanism Diagram */
        .lock-diagram {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .device-card {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .device-card.locked {
            border-color: #e74c3c;
            background: #ffe5e5;
        }
        
        .device-card.available {
            border-color: #27ae60;
            background: #e5ffe5;
        }
        
        .lock-icon {
            font-size: 30px;
            margin: 10px 0;
        }
        
        .device-status {
            font-weight: bold;
            color: #555;
            margin-top: 10px;
        }
        
        /* Conflict Resolution Diagram */
        .conflict-flow {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .decision-tree {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .decision-node {
            background: white;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            width: 300px;
            text-align: center;
        }
        
        .decision-options {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }
        
        .option-button {
            background: #ffc107;
            color: #333;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .option-button:hover {
            background: #ffb300;
            transform: translateY(-2px);
        }
        
        /* Interactive Demo */
        .demo-container {
            background: #f0f4f8;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .demo-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .demo-button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-display {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .status-value {
            font-weight: bold;
            color: #667eea;
        }
        
        /* Legend */
        .legend {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .legend-color {
            width: 30px;
            height: 20px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Workspace Data Annotation System</h1>
        
        <!-- Concept Overview -->
        <div class="diagram-section">
            <h2>Core Concept: Single Ownership Principle</h2>
            <div class="timeline-container">
                <h3>Device Data Timeline - Annotation Lifecycle</h3>
                <div class="timeline">
                    <div class="timeline-line"></div>
                    
                    <!-- Workspace A segment -->
                    <div class="timeline-segment workspace-a" style="left: 50px; width: 200px;">
                        Workspace A Active
                    </div>
                    
                    <!-- No workspace segment -->
                    <div class="timeline-segment no-workspace" style="left: 280px; width: 150px;">
                        No Workspace
                    </div>
                    
                    <!-- Workspace B segment -->
                    <div class="timeline-segment workspace-b" style="left: 460px; width: 200px;">
                        Workspace B Active
                    </div>
                    
                    <!-- Time labels -->
                    <div class="timeline-label" style="left: 50px;">9:00 AM</div>
                    <div class="timeline-label" style="left: 250px;">10:30 AM</div>
                    <div class="timeline-label" style="left: 430px;">11:00 AM</div>
                    <div class="timeline-label" style="left: 660px;">12:00 PM</div>
                    
                    <!-- Data points -->
                    <div class="data-point" style="left: 100px; background: #667eea;"></div>
                    <div class="data-point" style="left: 150px; background: #667eea;"></div>
                    <div class="data-point" style="left: 200px; background: #667eea;"></div>
                    <div class="data-point" style="left: 350px; background: #95a5a6;"></div>
                    <div class="data-point" style="left: 380px; background: #95a5a6;"></div>
                    <div class="data-point" style="left: 500px; background: #f093fb;"></div>
                    <div class="data-point" style="left: 550px; background: #f093fb;"></div>
                    <div class="data-point" style="left: 600px; background: #f093fb;"></div>
                </div>
                
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color workspace-a"></div>
                        <span>Data annotated with Workspace A</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color no-workspace"></div>
                        <span>Data without annotation</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color workspace-b"></div>
                        <span>Data annotated with Workspace B</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Annotation Rules -->
        <div class="diagram-section">
            <h2>Annotation Lifecycle Rules</h2>
            
            <h3>When Annotations Are Added ✅</h3>
            <div class="flow-diagram">
                <div class="flow-box">
                    <strong>Workspace Status</strong><br>
                    = ACTIVE
                    <div class="flow-arrow">→</div>
                </div>
                <div class="flow-box">
                    <strong>Device in Use</strong><br>
                    By Workspace
                    <div class="flow-arrow">→</div>
                </div>
                <div class="flow-box">
                    <strong>Device Connected</strong><br>
                    Not Orphaned
                    <div class="flow-arrow">→</div>
                </div>
                <div class="flow-box active">
                    <strong>Result</strong><br>
                    Data Gets Annotated
                </div>
            </div>
            
            <h3>When Annotations Are Removed ❌</h3>
            <div class="flow-diagram">
                <div class="flow-box">
                    <strong>ANY of these:</strong><br>
                    • Workspace → Standby/Closed<br>
                    • Device Disconnected<br>
                    • Device Orphaned<br>
                    • Device Released
                </div>
                <div class="flow-box active">
                    <strong>Result</strong><br>
                    Annotation Removed
                </div>
            </div>
        </div>
        
        <!-- Locking Mechanism -->
        <div class="diagram-section">
            <h2>Device Locking Mechanism</h2>
            
            <h3>Resource Types & Locking Behavior</h3>
            <div class="lock-diagram">
                <div class="device-card available">
                    <h4>📊 Workflow Runs</h4>
                    <div class="lock-icon">🔓</div>
                    <p>Existing locking works perfectly</p>
                    <div class="device-status">No Changes Needed</div>
                </div>
                
                <div class="device-card locked">
                    <h4>📈 Dashboard (In Workspace)</h4>
                    <div class="lock-icon">🔒</div>
                    <p>New locking feature required</p>
                    <div class="device-status">New Implementation</div>
                </div>
                
                <div class="device-card locked">
                    <h4>📏 Ad-hoc Measurement</h4>
                    <div class="lock-icon">🔒</div>
                    <p>New locking feature required</p>
                    <div class="device-status">New Implementation</div>
                </div>
                
                <div class="device-card available">
                    <h4>📊 Dashboard (Standalone)</h4>
                    <div class="lock-icon">🔓</div>
                    <p>Outside workspace - no locking</p>
                    <div class="device-status">Current Behavior Maintained</div>
                </div>
            </div>
        </div>
        
        <!-- Conflict Resolution -->
        <div class="diagram-section">
            <h2>Conflict Resolution Flow</h2>
            <div class="conflict-flow">
                <div class="decision-tree">
                    <div class="decision-node">
                        <strong>User Action:</strong><br>
                        "Start data collection in Workspace B"
                    </div>
                    
                    <div class="decision-node">
                        <strong>System Check:</strong><br>
                        Are the required devices already locked?
                    </div>
                    
                    <div class="decision-options">
                        <div class="decision-node" style="background: #e5ffe5; border-color: #27ae60;">
                            <strong>✅ No - Devices Available</strong><br>
                            1. Lock devices to Workspace B<br>
                            2. Start data collection<br>
                            3. Apply annotations
                        </div>
                        
                        <div class="decision-node" style="background: #ffe5e5; border-color: #e74c3c;">
                            <strong>⚠️ Yes - Conflict Detected</strong><br>
                            "Devices in use by Workspace A"<br><br>
                            <strong>Options:</strong><br>
                            <button class="option-button">Stop Workspace A & Start B</button>
                            <button class="option-button">Cancel Request</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Interactive Demo -->
        <div class="diagram-section">
            <h2>Interactive Demo - Try It Yourself!</h2>
            <div class="demo-container">
                <h3>Simulate Device Locking Scenarios</h3>
                <div class="demo-controls">
                    <button class="demo-button" onclick="simulateScenario('start-a')">Start Workspace A</button>
                    <button class="demo-button" onclick="simulateScenario('attempt-b')">Attempt Workspace B</button>
                    <button class="demo-button" onclick="simulateScenario('stop-a')">Stop Workspace A</button>
                    <button class="demo-button" onclick="simulateScenario('start-b')">Start Workspace B</button>
                    <button class="demo-button" onclick="simulateScenario('reset')">Reset Demo</button>
                </div>
                
                <div class="status-display" id="demo-status">
                    <div class="status-item">
                        <span>Current Active Workspace:</span>
                        <span class="status-value" id="active-workspace">None</span>
                    </div>
                    <div class="status-item">
                        <span>Device Lock Status:</span>
                        <span class="status-value" id="lock-status">Available</span>
                    </div>
                    <div class="status-item">
                        <span>Data Annotation:</span>
                        <span class="status-value" id="annotation-status">No annotations</span>
                    </div>
                    <div class="status-item">
                        <span>System Message:</span>
                        <span class="status-value" id="system-message">Ready to start</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentWorkspace = null;
        let deviceLocked = false;
        
        function simulateScenario(action) {
            const activeWorkspaceEl = document.getElementById('active-workspace');
            const lockStatusEl = document.getElementById('lock-status');
            const annotationEl = document.getElementById('annotation-status');
            const messageEl = document.getElementById('system-message');
            
            switch(action) {
                case 'start-a':
                    if (!deviceLocked) {
                        currentWorkspace = 'Workspace A';
                        deviceLocked = true;
                        activeWorkspaceEl.textContent = 'Workspace A';
                        activeWorkspaceEl.style.color = '#667eea';
                        lockStatusEl.textContent = 'Locked by Workspace A';
                        lockStatusEl.style.color = '#e74c3c';
                        annotationEl.textContent = 'Annotating with Workspace A';
                        annotationEl.style.color = '#667eea';
                        messageEl.textContent = '✅ Workspace A started successfully';
                        messageEl.style.color = '#27ae60';
                    } else {
                        messageEl.textContent = '⚠️ Devices already locked by ' + currentWorkspace;
                        messageEl.style.color = '#e74c3c';
                    }
                    break;
                    
                case 'attempt-b':
                    if (deviceLocked && currentWorkspace === 'Workspace A') {
                        messageEl.textContent = '⚠️ Conflict: Devices locked by Workspace A. Stop A first or cancel.';
                        messageEl.style.color = '#ffc107';
                    } else if (!deviceLocked) {
                        messageEl.textContent = 'ℹ️ Devices available. Use "Start Workspace B" to begin.';
                        messageEl.style.color = '#3498db';
                    }
                    break;
                    
                case 'stop-a':
                    if (currentWorkspace === 'Workspace A') {
                        currentWorkspace = null;
                        deviceLocked = false;
                        activeWorkspaceEl.textContent = 'None';
                        activeWorkspaceEl.style.color = '#95a5a6';
                        lockStatusEl.textContent = 'Available';
                        lockStatusEl.style.color = '#27ae60';
                        annotationEl.textContent = 'No annotations';
                        annotationEl.style.color = '#95a5a6';
                        messageEl.textContent = '✅ Workspace A stopped, devices released';
                        messageEl.style.color = '#27ae60';
                    } else {
                        messageEl.textContent = 'ℹ️ Workspace A is not active';
                        messageEl.style.color = '#3498db';
                    }
                    break;
                    
                case 'start-b':
                    if (!deviceLocked) {
                        currentWorkspace = 'Workspace B';
                        deviceLocked = true;
                        activeWorkspaceEl.textContent = 'Workspace B';
                        activeWorkspaceEl.style.color = '#f093fb';
                        lockStatusEl.textContent = 'Locked by Workspace B';
                        lockStatusEl.style.color = '#e74c3c';
                        annotationEl.textContent = 'Annotating with Workspace B';
                        annotationEl.style.color = '#f093fb';
                        messageEl.textContent = '✅ Workspace B started successfully';
                        messageEl.style.color = '#27ae60';
                    } else {
                        messageEl.textContent = '⚠️ Devices already locked by ' + currentWorkspace;
                        messageEl.style.color = '#e74c3c';
                    }
                    break;
                    
                case 'reset':
                    currentWorkspace = null;
                    deviceLocked = false;
                    activeWorkspaceEl.textContent = 'None';
                    activeWorkspaceEl.style.color = '#95a5a6';
                    lockStatusEl.textContent = 'Available';
                    lockStatusEl.style.color = '#27ae60';
                    annotationEl.textContent = 'No annotations';
                    annotationEl.style.color = '#95a5a6';
                    messageEl.textContent = 'Ready to start';
                    messageEl.style.color = '#3498db';
                    break;
            }
        }
    </script>
</body>
</html>